import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { Tabs, Tab } from '@heroui/react';
import './ExerciseDetailPage.scss';
import { apiService } from '../../services/api';
import {
  ExerciseDetailState,
  MuscleGroupInfo,
  InstructionStep,
  ExerciseTip
} from '../../types/exercise.types';
import { MUSCLE_CATEGORIES } from '../../constants/exerciseCategories';
import { MuscleGroupEnum } from '../../types/muscle.types';
import { StaticMuscleIllustration } from '../../components/fitness/MuscleVisualization/StaticMuscleIllustration';
import { smartMuscleMappingForExercise } from '../../utils/muscleUtils';

// 根据肌群ID获取肌群信息的工具函数
const getMuscleInfoById = (muscleId: number): { name: string; group: MuscleGroupEnum } | null => {
  const muscleData = MUSCLE_CATEGORIES[muscleId as keyof typeof MUSCLE_CATEGORIES];
  if (!muscleData) {
    // 静默跳过未映射的ID，不显示警告信息
    return null;
  }
  return {
    name: muscleData.name,
    group: muscleData.group
  };
};

// 从肌群ID数组中提取所有的MuscleGroupEnum值（用于高亮显示）
const extractMuscleGroups = (muscleIds: number[]): MuscleGroupEnum[] => {
  return muscleIds
    .map(id => getMuscleInfoById(id))
    .filter((info): info is NonNullable<typeof info> => info !== null)
    .map(info => info.group);
  };

// 应用智能肌肉映射
const applySmartMuscleMapping = (primaryIds: number[], secondaryIds: number[]) => {
  return smartMuscleMappingForExercise(primaryIds, secondaryIds);
};

// 肌肉群颜色配置类型
interface MuscleColorConfig {
  [key: string]: 'primary' | 'secondary';
}

// 难度显示组件 - 与ExercisesPage保持一致
const getDifficultyDisplay = (difficulty: string) => {
  const difficultyLevel = difficulty === 'beginner' ? 1 : difficulty === 'intermediate' ? 2 : 3;
  const iconPath = new URL(`../../assets/plates/plate${difficultyLevel === 1 ? '2-5' : difficultyLevel === 2 ? '5' : '10'}.png`, import.meta.url).href;
  
  return (
    <img 
      src={iconPath} 
      alt={difficulty}
      className="difficulty-plate"
      title={difficulty}
    />
  );
};

const ExerciseDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();

  // 获取从路由传递的动作信息
  const exerciseInfo = location.state as {
    name?: string;
    equipment?: string[];
    category?: string;
    difficulty?: string;
  } | null;

  const [state, setState] = useState<ExerciseDetailState>({
    detail: null,
    loading: true,
    error: null,
    videoUrl: null,
    targetMuscles: [],
    synergistMuscles: []
  });

  const [activeInstructionStep, setActiveInstructionStep] = useState<number>(0);
  const [isVideoPlaying, setIsVideoPlaying] = useState<boolean>(false);
  const [isFavorited, setIsFavorited] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<string>('muscles');

  const videoRef = useRef<HTMLVideoElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const tabsRef = useRef<HTMLDivElement>(null);

  // 获取动作详情数据
  const fetchExerciseDetail = useCallback(async () => {
    if (!id || isNaN(parseInt(id))) {
      setState(prev => ({ 
        ...prev, 
        loading: false, 
        error: '无效的动作ID' 
      }));
      return;
    }

    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const detail = await apiService.getExerciseDetail(parseInt(id));
      const videoUrl = detail.video_file ? apiService.buildVideoUrl(detail.video_file) : null;
      
      // 添加调试信息检查肌肉数据
      console.log('✅ 获取动作详情成功:', {
        exercise_id: detail.exercise_id,
        target_muscles_id: detail.target_muscles_id,
        synergist_muscles_id: detail.synergist_muscles_id
      });
      
      // 处理目标肌群和协同肌群 - 添加空值检查和调试信息
      const targetMuscles: MuscleGroupInfo[] = (detail.target_muscles_id || [])
        .map(muscleId => {
          const muscleInfo = getMuscleInfoById(muscleId);
          console.log(`🎯 目标肌肉 ${muscleId}:`, muscleInfo);
          return muscleInfo ? {
            id: muscleId,
            name: muscleInfo.name,
            type: 'target' as const
          } : null;
        })
        .filter((muscle): muscle is NonNullable<typeof muscle> => muscle !== null);

      const synergistMuscles: MuscleGroupInfo[] = (detail.synergist_muscles_id || [])
        .map(muscleId => {
          const muscleInfo = getMuscleInfoById(muscleId);
          console.log(`🤝 协同肌肉 ${muscleId}:`, muscleInfo);
          return muscleInfo ? {
            id: muscleId,
            name: muscleInfo.name,
            type: 'synergist' as const
          } : null;
        })
        .filter((muscle): muscle is NonNullable<typeof muscle> => muscle !== null);

      console.log('💪 处理后的肌肉数据:', { 
        targetMuscles: targetMuscles.length, 
        synergistMuscles: synergistMuscles.length,
        targetNames: targetMuscles.map(m => m.name),
        synergistNames: synergistMuscles.map(m => m.name)
      });

      setState(prev => ({
        ...prev,
        detail,
        videoUrl,
        targetMuscles,
        synergistMuscles,
        loading: false
      }));

    } catch (error) {
      console.error('获取动作详情失败:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '获取动作详情失败'
      }));
    }
  }, [id]);

  useEffect(() => {
    fetchExerciseDetail();
  }, [fetchExerciseDetail]);

  // iOS滚动优化 - 优化 tab 内容区域滚动
  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    if (!scrollContainer) return;

    // 启用iOS硬件加速滚动
    scrollContainer.style.setProperty('-webkit-overflow-scrolling', 'touch');
    scrollContainer.style.setProperty('overscroll-behavior', 'none');

    return () => {
      // 清理函数
    };
  }, [state.detail]);

  // 检测肌肉标签容器是否需要滚动
  useEffect(() => {
    const checkScrollable = () => {
      const muscleSidebar = document.querySelector('.muscle-list-sidebar');
      console.log('🔍 检测整体滚动容器:', muscleSidebar ? '找到' : '未找到');

      if (!muscleSidebar) {
        console.log('⚠️ 未找到 .muscle-list-sidebar 容器，可能DOM还未渲染完成');
        return;
      }

      const element = muscleSidebar as HTMLElement;
      const scrollHeight = element.scrollHeight;
      const clientHeight = element.clientHeight;
      const needsScroll = scrollHeight > clientHeight;

      // 获取容器内的标签总数量
      const totalTagCount = element.querySelectorAll('.muscle-tag').length;
      const categoryCount = element.querySelectorAll('.muscle-category').length;

      console.log('📏 整体滚动容器状态:', {
        totalTagCount,
        categoryCount,
        scrollHeight,
        clientHeight,
        containerHeight: getComputedStyle(element).height,
        needsScroll,
        hasScrollableClass: element.classList.contains('scrollable')
      });

      if (needsScroll) {
        element.classList.add('scrollable');
        console.log('✅ 整体容器添加 scrollable 类');
      } else {
        element.classList.remove('scrollable');
        console.log('❌ 整体容器移除 scrollable 类');
      }
    };

    // 多次检测，确保DOM和样式完全加载
    const timeoutIds = [
      setTimeout(checkScrollable, 100),
      setTimeout(checkScrollable, 300),
      setTimeout(checkScrollable, 500)
    ];

    // 监听窗口大小变化
    window.addEventListener('resize', checkScrollable);

    return () => {
      timeoutIds.forEach(id => clearTimeout(id));
      window.removeEventListener('resize', checkScrollable);
    };
  }, [state.targetMuscles, state.synergistMuscles]);

  // 手动触发滚动检测（开发调试用）
  const triggerScrollCheck = useCallback(() => {
    const muscleSidebar = document.querySelector('.muscle-list-sidebar');
    console.log('🔧 手动触发整体滚动检测:', muscleSidebar ? '找到容器' : '未找到容器');

    if (!muscleSidebar) return;

    const element = muscleSidebar as HTMLElement;
    const scrollHeight = element.scrollHeight;
    const clientHeight = element.clientHeight;
    const needsScroll = scrollHeight > clientHeight;

    console.log('🔧 手动检测整体容器:', {
      scrollHeight,
      clientHeight,
      needsScroll
    });

    if (needsScroll) {
      element.classList.add('scrollable');
    } else {
      element.classList.remove('scrollable');
    }
  }, []);

  // 在开发环境下暴露到全局，方便调试
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      (window as any).triggerScrollCheck = triggerScrollCheck;
      console.log('🔧 开发模式：可以在控制台调用 triggerScrollCheck() 手动检测滚动');
    }
  }, [triggerScrollCheck]);

  // 返回按钮处理
  const handleGoBack = useCallback(() => {
    navigate(-1);
  }, [navigate]);

  // 重试按钮处理
  const handleRetry = useCallback(() => {
    fetchExerciseDetail();
  }, [fetchExerciseDetail]);

  // 收藏按钮处理
  const handleFavoriteToggle = useCallback(() => {
    setIsFavorited(prev => !prev);
    // TODO: 实现收藏功能的API调用
  }, []);

  // 指导步骤处理
  const handleInstructionStepClick = useCallback((stepIndex: number) => {
    setActiveInstructionStep(stepIndex);
  }, []);

  // 视频播放控制
  const handleVideoClick = useCallback(() => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
        setIsVideoPlaying(true);
      } else {
        videoRef.current.pause();
        setIsVideoPlaying(false);
      }
    }
  }, []);

  // 视频播放状态监听
  const handleVideoPlay = useCallback(() => {
    setIsVideoPlaying(true);
  }, []);

  const handleVideoPause = useCallback(() => {
    setIsVideoPlaying(false);
  }, []);

  // 转换指导步骤为组件数据
  const instructionSteps: InstructionStep[] = state.detail?.ex_instructions.map((content, index) => ({
    id: index,
    content,
    isActive: index === activeInstructionStep
  })) || [];

  // 转换提示为组件数据
  const exerciseTips: ExerciseTip[] = state.detail?.exercise_tips.map((content, index) => ({
    id: index,
    content,
    type: content.includes('避免') || content.includes('错误') ? 'warning' as const : 'info' as const
  })) || [];

  // 获取要高亮的肌群组合（应用智能映射）
  const highlightedMuscleGroups: MuscleGroupEnum[] = useMemo(() => {
    if (!state.detail) return [];
    
    // 应用智能肌肉映射
    const { adjustedPrimary, adjustedSecondary } = applySmartMuscleMapping(
      state.detail.target_muscles_id,
      state.detail.synergist_muscles_id
    );
    
    const targetGroups = extractMuscleGroups(adjustedPrimary);
    const synergistGroups = extractMuscleGroups(adjustedSecondary);
    
    // 去重并合并
    return [...new Set([...targetGroups, ...synergistGroups])];
  }, [state.detail]);

  // 肌群颜色映射（区分主要肌肉和协同肌肉，应用智能映射，主要肌肉优先）
  const muscleColorConfig: MuscleColorConfig = useMemo(() => {
    if (!state.detail) return {};
    
    const config: MuscleColorConfig = {};
    
    // 应用智能肌肉映射
    const { adjustedPrimary, adjustedSecondary } = applySmartMuscleMapping(
      state.detail.target_muscles_id,
      state.detail.synergist_muscles_id
    );
    
    // 先设置协同肌群为次要颜色
    const synergistGroups = extractMuscleGroups(adjustedSecondary);
    synergistGroups.forEach(group => {
      config[group] = 'secondary';
    });
    
    // 后设置目标肌群为主要颜色（会覆盖重复的肌肉群，确保主要肌肉优先）
    const targetGroups = extractMuscleGroups(adjustedPrimary);
    targetGroups.forEach(group => {
      config[group] = 'primary';
    });
    
    return config;
  }, [state.detail]);

  // Tab 数据结构 - 符合 HeroUI 标准
  const tabsData = useMemo(() => {
    const tabs = [
      {
        id: "muscles",
        label: "训练部位",
        content: (
          <div className="muscle-info-section">
            <div className="muscle-info-card">
              {/* 左侧：肌肉列表（20%宽度） */}
              <div className="muscle-list-sidebar">
                {/* 主要肌群 */}
                {state.targetMuscles && state.targetMuscles.length > 0 && (
                  <div className="muscle-category">
                    <h3>主要</h3>
                    <div className="muscle-tags">
                      {state.targetMuscles.map(muscle => (
                        <div key={muscle.id} className="muscle-tag primary">
                          {muscle.name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 次要肌群 */}
                {state.synergistMuscles && state.synergistMuscles.length > 0 && (
                  <div className="muscle-category">
                    <h3>次要</h3>
                    <div className="muscle-tags">
                      {state.synergistMuscles.map(muscle => (
                        <div key={muscle.id} className="muscle-tag secondary">
                          {muscle.name}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* 右侧：肌肉图（80%宽度） */}
              <div className="muscle-illustration-container">
                {highlightedMuscleGroups.length > 0 ? (
                  <StaticMuscleIllustration
                    selectedMuscles={highlightedMuscleGroups}
                    theme="light"
                    muscleColorConfig={muscleColorConfig}
                  />
                ) : (
                  <div className="no-muscle-data">
                    <p>暂无肌群数据</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )
      },
      {
        id: "instructions",
        label: "动作指导",
        content: (
          <div className="instructions-section">
            <div className="instruction-steps">
              {instructionSteps.map((step, index) => (
                <div
                  key={step.id}
                  className={`instruction-step ${step.isActive ? 'active' : ''}`}
                  onClick={() => handleInstructionStepClick(index)}
                >
                  <div className="step-number">{index + 1}</div>
                  <div className="step-content">{step.content}</div>
                </div>
              ))}
            </div>
          </div>
        )
      }
    ];

    // 只有当有注意事项时才添加该 Tab
    if (exerciseTips.length > 0) {
      tabs.push({
        id: "tips",
        label: "注意事项",
        content: (
          <div className="tips-section">
            <div className="exercise-tips">
              {exerciseTips.map(tip => (
                <div key={tip.id} className={`tip-item ${tip.type}`}>
                  <div className="tip-icon">
                    {tip.type === 'warning' ? (
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                        <line x1="12" y1="9" x2="12" y2="13"/>
                        <line x1="12" y1="17" x2="12.01" y2="17"/>
                      </svg>
                    ) : (
                      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M12 16v-4M12 8h.01"/>
                      </svg>
                    )}
                  </div>
                  <div className="tip-content">{tip.content}</div>
                </div>
              ))}
            </div>
          </div>
        )
      });
    }

    return tabs;
  }, [state.targetMuscles, state.synergistMuscles, highlightedMuscleGroups, muscleColorConfig, instructionSteps, exerciseTips, handleInstructionStepClick]);

  // Loading状态
  if (state.loading) {
    return (
      <div className="exercise-detail-page">
        <div className="exercise-detail-header">
          <button className="back-btn" onClick={handleGoBack}>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
          </button>
          <div className="fixed-title">
            <h1>加载中...</h1>
          </div>
          <div className="header-placeholder"></div>
        </div>

        <div className="exercise-detail-content">
          <div className="loading-state">
            <div className="loading-spinner">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" className="spinning">
                <circle cx="12" cy="12" r="10"/>
                <path d="M12 2v4M12 18v4M4.93 4.93l2.83 2.83M16.24 16.24l2.83 2.83M2 12h4M18 12h4M4.93 19.07l2.83-2.83M16.24 7.76l2.83-2.83"/>
              </svg>
            </div>
            <p>正在加载动作详情...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error状态
  if (state.error) {
    return (
      <div className="exercise-detail-page">
        <div className="exercise-detail-header">
          <button className="back-btn" onClick={handleGoBack}>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M19 12H5M12 19l-7-7 7-7"/>
            </svg>
          </button>
          <div className="fixed-title">
            <h1>加载失败</h1>
          </div>
          <div className="header-placeholder"></div>
        </div>

        <div className="exercise-detail-content">
          <div className="error-state">
            <div className="error-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"/>
                <line x1="15" y1="9" x2="9" y2="15"/>
                <line x1="9" y1="9" x2="15" y2="15"/>
              </svg>
            </div>
            <h3>获取动作详情失败</h3>
            <p>{state.error}</p>
            <div className="error-actions">
              <button className="retry-btn" onClick={handleRetry}>
                重试
              </button>
              <button className="back-btn-secondary" onClick={handleGoBack}>
                返回
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // 主要内容渲染
  return (
    <div className="exercise-detail-page">
      {/* 固定头部 - 始终显示动作名称 */}
      <div className="exercise-detail-header">
        <button className="back-btn" onClick={handleGoBack}>
          <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M19 12H5M12 19l-7-7 7-7"/>
          </svg>
        </button>

        {/* 固定标题 - 始终显示 */}
        <div className="fixed-title">
          <h1>{exerciseInfo?.name || `动作详情 #${state.detail?.exercise_id}`}</h1>
        </div>

        <button
          className={`favorite-btn ${isFavorited ? 'favorited' : ''}`}
          onClick={handleFavoriteToggle}
        >
          <svg viewBox="0 0 24 24" fill={isFavorited ? "currentColor" : "none"} stroke="currentColor">
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
          </svg>
        </button>
      </div>

      {/* 内容容器 - 重新设计滚动结构 */}
      <div className="exercise-detail-content">
        {/* 固定视频播放区域 - 不参与滚动 */}
        {state.videoUrl && (
          <div className="video-section fixed-video">
            <div className="video-container" onClick={handleVideoClick}>
              <video
                ref={videoRef}
                src={state.videoUrl}
                autoPlay
                loop
                muted
                playsInline
                poster={state.detail?.video_file ? state.videoUrl.replace('.mp4', '-poster.jpg') : undefined}
                className="exercise-video"
                onPlay={handleVideoPlay}
                onPause={handleVideoPause}
              >
                您的浏览器不支持视频播放
              </video>

              {/* 播放/暂停按钮覆盖层 */}
              <div className="video-controls-overlay">
                <button className={`play-pause-btn ${isVideoPlaying ? 'playing' : 'paused'}`}>
                  {isVideoPlaying ? (
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <rect x="6" y="4" width="4" height="16"/>
                      <rect x="14" y="4" width="4" height="16"/>
                    </svg>
                  ) : (
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <polygon points="5,3 19,12 5,21"/>
                    </svg>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* 固定的动作信息区域 */}
        {exerciseInfo && (
          <div className="exercise-info-section fixed-info">
            <div className="exercise-info-card">
              <div className="exercise-meta">
                {exerciseInfo.equipment && exerciseInfo.equipment.length > 0 && (
                  <span className="meta-item">器械：{exerciseInfo.equipment.join(', ')}</span>
                )}
                {exerciseInfo.category && (
                  <span className="meta-item">类别：{exerciseInfo.category}</span>
                )}
                {exerciseInfo.difficulty && (
                  <div className="difficulty">
                    {getDifficultyDisplay(exerciseInfo.difficulty)}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 固定的 Tab 切换栏 */}
        <div className="exercise-tabs-section fixed-tabs" ref={tabsRef}>
          <Tabs
            selectedKey={activeTab}
            onSelectionChange={(key) => setActiveTab(key as string)}
            className="exercise-tabs"
            variant="underlined"
            color="primary"
            aria-label="动作详情选项卡"
            items={tabsData}
          >
            {(item) => (
              <Tab key={item.id} title={item.label}>
                {/* Tab 内容将在下方的可滚动区域中显示 */}
              </Tab>
            )}
          </Tabs>
        </div>

        {/* 可滚动的 Tab 内容区域 */}
        <div className="tab-content-scrollable" ref={scrollContainerRef}>
          <div className="tab-content-wrapper">
            {tabsData.find(tab => tab.id === activeTab)?.content}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExerciseDetailPage; 