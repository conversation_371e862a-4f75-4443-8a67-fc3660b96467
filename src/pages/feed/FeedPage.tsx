import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  FeedFilterType,
  FeedPageState
} from '../../types/feed.types';
import { WorkoutCarousel } from '../../components/fitness/WorkoutCarousel/WorkoutCarousel';
import { CommunityService } from '../../services/communityService';
import { authService } from '../../services/authService';
import { formatPostTime } from '../../utils/timeFormatter';
import './FeedPage.scss';



const FeedPage: React.FC = () => {
  // 状态管理
  const [state, setState] = useState<FeedPageState>({
    posts: [],
    filter: 'all',
    loading: false,
    error: null,
    hasMore: true,
    page: 0
  });

  // 解构状态
  const { posts, filter, loading, error, hasMore, page } = state;

  // 加载帖子数据
  const loadPosts = useCallback(async (pageNum: number = 0, isRefresh: boolean = false) => {
    try {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null
      }));

      // 确保用户已认证
      if (!authService.isAuthenticated()) {
        console.log('【FeedPage】用户未认证，开始登录');
        await authService.loginWithTestUser();
      }

      console.log('【FeedPage】开始加载帖子数据:', { pageNum, isRefresh });

      const response = await CommunityService.getPosts({
        skip: pageNum * 20,
        limit: 20
      });

      console.log('【FeedPage】帖子数据加载成功:', response);

      setState(prev => ({
        ...prev,
        posts: isRefresh ? response.posts : [...prev.posts, ...response.posts],
        hasMore: response.hasMore,
        page: pageNum,
        loading: false,
        error: null
      }));

    } catch (error) {
      console.error('【FeedPage】加载帖子失败:', error);
      setState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : '加载失败，请稍后重试'
      }));
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    loadPosts(0, true);
  }, [loadPosts]);



  // 处理点赞
  const handleLike = useCallback(async (postId: string) => {
    try {
      console.log('【FeedPage】点赞帖子:', postId);

      // 调用API
      await CommunityService.togglePostLike(postId);

      // 更新本地状态
      setState(prev => ({
        ...prev,
        posts: prev.posts.map(post => {
          if (post.id === postId) {
            return {
              ...post,
              isLiked: !post.isLiked,
              stats: {
                ...post.stats,
                likes: post.isLiked ? post.stats.likes - 1 : post.stats.likes + 1
              }
            };
          }
          return post;
        })
      }));
    } catch (error) {
      console.error('【FeedPage】点赞失败:', error);
      // 可以添加错误提示
    }
  }, []);

  // 处理筛选器变化
  const handleFilterChange = useCallback((newFilter: FeedFilterType) => {
    setState(prev => ({ ...prev, filter: newFilter }));
  }, []);

  // 刷新数据
  const handleRefresh = useCallback(() => {
    loadPosts(0, true);
  }, [loadPosts]);

  // 加载更多
  const handleLoadMore = useCallback(() => {
    if (!loading && hasMore) {
      loadPosts(page + 1, false);
    }
  }, [loading, hasMore, page, loadPosts]);

  // 过滤帖子
  const filteredPosts = useMemo(() => {
    return posts.filter(post => {
      switch (filter) {
        case 'workouts':
          return post.content.workout;
        case 'following':
          // 这里可以根据关注关系过滤
          return true;
        default:
          return true;
      }
    });
  }, [posts, filter]);

  return (
    <div className="feed-page">
      {/* Feed Header */}
      <div className="feed-header">
        <div className="feed-title">
          <h1>动态</h1>
          <p>发现健身伙伴的最新动态</p>
        </div>

        {/* Filter Tabs */}
        <div className="feed-filters">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => handleFilterChange('all')}
          >
            全部动态
          </button>
          <button
            className={`filter-btn ${filter === 'workouts' ? 'active' : ''}`}
            onClick={() => handleFilterChange('workouts')}
          >
            训练记录
          </button>
          <button
            className={`filter-btn ${filter === 'following' ? 'active' : ''}`}
            onClick={() => handleFilterChange('following')}
          >
            关注的人
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && posts.length === 0 && (
        <div className="loading-state">
          <div className="loading-spinner"></div>
          <p>正在加载动态...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="error-state">
          <div className="error-icon">⚠️</div>
          <p>{error}</p>
          <button className="retry-btn" onClick={handleRefresh}>
            重试
          </button>
        </div>
      )}

      {/* Empty State */}
      {!loading && !error && posts.length === 0 && (
        <div className="empty-state">
          <div className="empty-icon">📝</div>
          <p>暂无动态内容</p>
          <button className="refresh-btn" onClick={handleRefresh}>
            刷新
          </button>
        </div>
      )}

      {/* Create Post */}
      <div className="create-post-card">
        <div className="create-post-header">
          <div className="user-avatar">
            <img src="/api/placeholder/40/40" alt="Your avatar" />
          </div>
          <button className="create-post-input">
            分享你的健身动态...
          </button>
        </div>
        <div className="create-post-actions">
          <button className="post-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M15 10L11 14L17 20L21 4L3 11L9 13V19L12 16"/>
            </svg>
            分享训练
          </button>
          <button className="post-action-btn">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="8.5" cy="8.5" r="1.5"/>
              <path d="M21 15L16 10L5 21"/>
            </svg>
            上传图片
          </button>
        </div>
      </div>

      {/* Feed Posts */}
      <div className="feed-posts">
        {filteredPosts.map(post => (
          <article key={post.id} className="feed-post">
            {/* Post Header */}
            <div className="post-header">
              {/* 左侧：用户头像 + 用户名称 + 发布时间 */}
              <div className="post-user-section">
                <div className="user-avatar">
                  <img src={post.user.avatar} alt={post.user.name} />
                </div>
                <div className="user-info">
                  <div className="user-name-row">
                    <span className="user-name">
                      {post.user.name}
                      {post.user.isVerified && (
                        <svg className="verified-icon" viewBox="0 0 24 24" fill="currentColor">
                          <path d="M12 2L13.09 5.26L16 6L13.09 6.74L12 10L10.91 6.74L8 6L10.91 5.26L12 2Z"/>
                          <path d="M12 12L13.09 15.26L16 16L13.09 16.74L12 20L10.91 16.74L8 16L10.91 15.26L12 12Z"/>
                        </svg>
                      )}
                    </span>
                  </div>
                  <div className="post-timestamp">
                    {formatPostTime(post.timestamp)}
                  </div>
                </div>
              </div>

              {/* 右侧：关注状态按钮 */}
              <div className="post-actions">
                {/* TODO: 根据关注状态显示不同按钮 */}
                <button className="follow-btn" aria-label="关注">
                  关注
                </button>
                {/* 已关注时显示三点菜单 */}
                {/* <button className="post-menu-btn" aria-label="更多选项">
                  <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <circle cx="12" cy="12" r="1"/>
                    <circle cx="19" cy="12" r="1"/>
                    <circle cx="5" cy="12" r="1"/>
                  </svg>
                </button> */}
              </div>
            </div>

            {/* Post Content */}
            <div className="post-content">
              {post.content.text && (
                <p className="post-text">{post.content.text}</p>
              )}

              {/* 训练统计信息 - 移到post-text下方 */}
              {post.content.workout && (
                <div className="workout-stats-summary">
                  <div className="workout-stat">
                    <span className="stat-value">{Math.round(post.content.workout.duration_seconds / 60)}</span>
                    <span className="stat-label">分钟</span>
                  </div>
                  <div className="workout-stat">
                    <span className="stat-value">{post.content.workout.total_volume}</span>
                    <span className="stat-label">kg</span>
                  </div>
                  <div className="workout-stat">
                    <span className="stat-value">{post.content.workout.calories_burned || 0}</span>
                    <span className="stat-label">卡路里</span>
                  </div>
                  <div className="workout-stat">
                    <span className="stat-value">{post.content.workout.exercises.reduce((sum, ex) => sum + ex.sets, 0)}</span>
                    <span className="stat-label">组数</span>
                  </div>
                </div>
              )}

              {/* 训练记录轮播图 - 简化嵌套结构 */}
              {post.content.workout && post.content.carousel_items && post.content.carousel_items.length > 0 && (
                <div className="workout-carousel-section">
                  <WorkoutCarousel
                    items={post.content.carousel_items}
                    showIndicators={true}
                    className="workout-post-carousel"
                  />
                </div>
              )}
            </div>

            {/* Post Actions */}
            <div className="post-actions">
              <button 
                className={`action-btn like-btn ${post.isLiked ? 'liked' : ''}`}
                onClick={() => handleLike(post.id)}
              >
                <svg viewBox="0 0 24 24" fill={post.isLiked ? "currentColor" : "none"} stroke="currentColor">
                  <path d="M20.84 4.61C20.3292 4.099 19.7228 3.69364 19.0554 3.41708C18.3879 3.14052 17.6725 2.99817 16.95 2.99817C16.2275 2.99817 15.5121 3.14052 14.8446 3.41708C14.1772 3.69364 13.5708 4.099 13.06 4.61L12 5.67L10.94 4.61C9.9083 3.57831 8.50903 2.99871 7.05 2.99871C5.59096 2.99871 4.19169 3.57831 3.16 4.61C2.1283 5.64169 1.54871 7.04096 1.54871 8.5C1.54871 9.95904 2.1283 11.3583 3.16 12.39L12 21.23L20.84 12.39C21.351 11.8792 21.7563 11.2728 22.0329 10.6053C22.3095 9.93789 22.4518 9.22248 22.4518 8.5C22.4518 7.77752 22.3095 7.06211 22.0329 6.39468C21.7563 5.72725 21.351 5.12087 20.84 4.61Z"/>
                </svg>
                <span>{post.stats.likes}</span>
              </button>
              
              <button className="action-btn comment-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z"/>
                </svg>
                <span>{post.stats.comments}</span>
              </button>
              
              <button className="action-btn share-btn">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="18" cy="5" r="3"/>
                  <circle cx="6" cy="12" r="3"/>
                  <circle cx="18" cy="19" r="3"/>
                  <path d="M8.59 13.51L15.42 17.49"/>
                  <path d="M15.41 6.51L8.59 10.49"/>
                </svg>
                <span>{post.stats.shares}</span>
              </button>
            </div>
          </article>
        ))}
      </div>

      {/* Load More */}
      {hasMore && !loading && posts.length > 0 && (
        <div className="load-more">
          <button className="load-more-btn" onClick={handleLoadMore}>
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            查看更多动态
          </button>
        </div>
      )}

      {/* Loading More */}
      {loading && posts.length > 0 && (
        <div className="loading-more">
          <div className="loading-spinner small"></div>
          <p>加载更多...</p>
        </div>
      )}
    </div>
  );
};

export default FeedPage; 