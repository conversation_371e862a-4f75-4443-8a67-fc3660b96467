/**
 * API客户端工具
 * 集成认证中间件，确保所有API请求都包含正确的认证头
 */

import { AuthService } from '../services/authService';

// API响应基础接口
export interface ApiResponse<T = any> {
  success?: boolean;
  data?: T;
  message?: string;
  errors?: string[];
  detail?: string;
}

// 分页响应接口
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  has_more: boolean;
}

// 请求配置接口
export interface RequestConfig extends RequestInit {
  timeout?: number;
  retries?: number;
  skipAuth?: boolean;
  baseUrl?: string;
}

// 错误类型定义
export class ApiError extends Error {
  public status: number;
  public response?: Response;
  public data?: any;

  constructor(message: string, status: number, response?: Response, data?: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.response = response;
    this.data = data;
  }
}

/**
 * API客户端类
 */
export class ApiClient {
  private baseUrl: string;
  private defaultTimeout: number;
  private defaultRetries: number;
  private authService: AuthService;

  constructor(
    baseUrl: string = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
    authServiceInstance?: AuthService
  ) {
    this.baseUrl = baseUrl;
    this.defaultTimeout = 10000;
    this.defaultRetries = 3;
    // 如果没有提供认证服务实例，则创建一个新的
    this.authService = authServiceInstance || new AuthService();

    console.log('【API客户端】初始化完成:', { baseUrl: this.baseUrl });
  }

  /**
   * 通用请求方法
   */
  async request<T = any>(
    endpoint: string,
    config: RequestConfig = {}
  ): Promise<T> {
    const {
      timeout = this.defaultTimeout,
      retries = this.defaultRetries,
      skipAuth = false,
      baseUrl = this.baseUrl,
      ...requestInit
    } = config;

    const url = `${baseUrl}${endpoint}`;
    let lastError: Error;

    // 重试机制
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        console.log(`【API客户端】请求尝试 ${attempt + 1}/${retries + 1}:`, url);

        // 准备请求头
        const headers = await this.prepareHeaders(requestInit.headers, skipAuth);

        // 准备请求配置
        const requestConfig: RequestInit = {
          ...requestInit,
          headers,
          signal: this.createTimeoutSignal(timeout)
        };

        console.log('【API客户端】请求配置:', {
          method: requestConfig.method || 'GET',
          url,
          headers: this.sanitizeHeaders(headers),
          hasBody: !!requestConfig.body
        });

        // 发起请求
        const response = await fetch(url, requestConfig);

        // 处理响应
        return await this.handleResponse<T>(response);

      } catch (error) {
        lastError = error as Error;
        
        console.error(`【API客户端】请求失败 (尝试 ${attempt + 1}):`, {
          url,
          error: lastError.message,
          willRetry: attempt < retries
        });

        // 如果是认证错误，尝试刷新token后重试
        if (error instanceof ApiError && error.status === 401 && !skipAuth) {
          console.log('【API客户端】检测到认证错误，尝试刷新token');
          try {
            await this.authService.refreshTokenIfNeeded();
            continue; // 重试当前请求
          } catch (refreshError) {
            console.error('【API客户端】刷新token失败:', refreshError);
            throw new ApiError('认证失效，请重新登录', 401);
          }
        }

        // 如果是最后一次尝试，抛出错误
        if (attempt === retries) {
          throw lastError;
        }

        // 等待后重试
        await this.delay(Math.pow(2, attempt) * 1000);
      }
    }

    throw lastError!;
  }

  /**
   * GET请求
   */
  async get<T = any>(
    endpoint: string,
    params?: Record<string, any>,
    config?: RequestConfig
  ): Promise<T> {
    const url = params ? `${endpoint}?${this.buildQueryString(params)}` : endpoint;
    
    return this.request<T>(url, {
      method: 'GET',
      ...config
    });
  }

  /**
   * POST请求
   */
  async post<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
      ...config
    });
  }

  /**
   * PUT请求
   */
  async put<T = any>(
    endpoint: string,
    data?: any,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
      ...config
    });
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(
    endpoint: string,
    config?: RequestConfig
  ): Promise<T> {
    return this.request<T>(endpoint, {
      method: 'DELETE',
      ...config
    });
  }

  /**
   * 准备请求头
   */
  private async prepareHeaders(
    customHeaders?: HeadersInit,
    skipAuth: boolean = false
  ): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    };

    // 添加自定义头
    if (customHeaders) {
      Object.assign(headers, customHeaders);
    }

    // 添加认证头
    if (!skipAuth) {
      try {
        // 确保token有效
        await this.authService.refreshTokenIfNeeded();
        
        // 获取认证头
        const authHeaders = this.authService.getAuthHeaders();
        Object.assign(headers, authHeaders);
        
      } catch (error) {
        console.warn('【API客户端】获取认证头失败:', error);
        // 如果获取认证头失败，继续请求但不包含认证信息
      }
    }

    return headers;
  }

  /**
   * 处理响应
   */
  private async handleResponse<T>(response: Response): Promise<T> {
    const contentType = response.headers.get('content-type');
    const isJson = contentType?.includes('application/json');

    let data: any;
    try {
      data = isJson ? await response.json() : await response.text();
    } catch (error) {
      console.error('【API客户端】解析响应数据失败:', error);
      data = null;
    }

    console.log('【API客户端】响应数据:', {
      status: response.status,
      statusText: response.statusText,
      contentType,
      data: typeof data === 'string' ? data.substring(0, 200) : data
    });

    // 检查响应状态
    if (!response.ok) {
      const errorMessage = this.extractErrorMessage(data, response);
      throw new ApiError(errorMessage, response.status, response, data);
    }

    // 返回数据
    if (isJson && data && typeof data === 'object') {
      // 如果响应包含标准的API响应格式，返回data字段
      if ('data' in data) {
        return data.data;
      }
      // 否则返回整个响应
      return data;
    }

    return data;
  }

  /**
   * 提取错误消息
   */
  private extractErrorMessage(data: any, response: Response): string {
    if (data && typeof data === 'object') {
      return data.message || data.detail || data.error || `请求失败: ${response.status}`;
    }
    
    if (typeof data === 'string') {
      return data || `请求失败: ${response.status}`;
    }

    return `请求失败: ${response.status} ${response.statusText}`;
  }

  /**
   * 构建查询字符串
   */
  private buildQueryString(params: Record<string, any>): string {
    const searchParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        searchParams.append(key, String(value));
      }
    });

    return searchParams.toString();
  }

  /**
   * 创建超时信号
   */
  private createTimeoutSignal(timeout: number): AbortSignal {
    const controller = new AbortController();
    
    setTimeout(() => {
      controller.abort();
    }, timeout);

    return controller.signal;
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理敏感头信息用于日志
   */
  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized = { ...headers };
    
    if (sanitized.Authorization) {
      sanitized.Authorization = 'Bearer ***';
    }

    return sanitized;
  }

  /**
   * 设置基础URL
   */
  setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
    console.log('【API客户端】基础URL已更新:', baseUrl);
  }

  /**
   * 获取当前基础URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}

// 创建默认实例
export const apiClient = new ApiClient();

// 导出默认实例
export default apiClient;
