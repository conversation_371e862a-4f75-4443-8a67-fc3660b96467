/**
 * API配置文件
 * 支持不同环境的服务器配置和网络设置
 */

import { Capacitor } from '@capacitor/core';

// 环境类型
export type Environment = 'development' | 'staging' | 'production';

// API配置接口
export interface ApiConfig {
  baseURL: string;
  imageBaseURL: string;
  timeout: number;
  retries: number;
  retryDelay: number;
  enableLogging: boolean;
}

// 环境配置
const configs: Record<Environment, ApiConfig> = {
  development: {
    // 🚨 硬编码HTTP服务器地址 - 确保iOS端使用正确服务器
    baseURL: 'http://124.222.91.101:8000',
    imageBaseURL: 'http://124.222.91.101:8000/exercises/images',
    timeout: 15000, // 增加超时时间用于开发环境
    retries: 3,
    retryDelay: 1000,
    enableLogging: true
  },
  
  staging: {
    baseURL: 'https://api-staging.fitmaster.com',
    imageBaseURL: 'https://api-staging.fitmaster.com/exercises/images',
    timeout: 10000,
    retries: 3,
    retryDelay: 1000,
    enableLogging: true
  },

  production: {
    baseURL: 'https://api.fitmaster.com',
    imageBaseURL: 'https://api.fitmaster.com/exercises/images',
    timeout: 8000,
    retries: 2,
    retryDelay: 1500,
    enableLogging: false
  }
};

// 获取当前环境
export const getCurrentEnvironment = (): Environment => {
  // 🚨 强制开发环境 - 用于HTTP服务器测试
  // 这是为了确保iOS端使用HTTP服务器而不是HTTPS生产服务器
  console.log('🔧 强制使用开发环境配置 (HTTP服务器)');
  return 'development';
};

// 获取当前配置
export const getApiConfig = (): ApiConfig => {
  const env = getCurrentEnvironment();
  const config = configs[env];
  
  // 调试日志
  console.log('🔧 API配置加载:', {
    environment: env,
    baseURL: config.baseURL,
    imageBaseURL: config.imageBaseURL
  });
  
  return config;
};

// 平台特定的网络配置
export const getPlatformNetworkConfig = () => {
  const platform = Capacitor.getPlatform();
  const isNative = Capacitor.isNativePlatform();
  
  return {
    platform,
    isNative,
    // iOS特定配置
    iosRequiresHTTPS: platform === 'ios' && !import.meta.env.DEV,
    // Android特定配置
    androidAllowsCleartext: platform === 'android',
    // Web特定配置
    webSupportsCORS: platform === 'web'
  };
};

// 网络错误处理配置
export const getNetworkErrorConfig = () => {
  return {
    shouldRetry: (error: Error): boolean => {
      // 网络连接错误
      if (error.message.includes('Failed to fetch') || 
          error.message.includes('NetworkError') ||
          error.message.includes('ERR_NETWORK')) {
        return true;
      }
      
      // 超时错误
      if (error.message.includes('timeout') || 
          error.message.includes('TimeoutError')) {
        return true;
      }
      
      // 服务器错误（5xx）
      if (error.message.includes('500') || 
          error.message.includes('502') || 
          error.message.includes('503')) {
        return true;
      }
      
      return false;
    },
    
    getErrorMessage: (error: Error): string => {
      const platform = Capacitor.getPlatform();
      
      if (error.message.includes('Failed to fetch')) {
        if (platform === 'ios') {
          return '网络连接失败。如果您使用的是HTTP服务器，请确保已在iOS配置中允许不安全连接。';
        }
        return '网络连接失败，请检查网络设置。';
      }
      
      if (error.message.includes('timeout')) {
        return '请求超时，请检查网络连接或稍后重试。';
      }
      
      if (error.message.includes('500')) {
        return '服务器内部错误，请稍后重试。';
      }
      
      return error.message || '未知网络错误';
    },
    
    shouldShowRetryButton: (error: Error): boolean => {
      return !error.message.includes('401') && !error.message.includes('403');
    }
  };
};

// 导出默认配置
export default getApiConfig(); 