// WorkoutCarousel 组件样式
.workout-carousel {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: var(--radius-lg);
  background: var(--bg-surface);

  // 强制覆盖ExerciseList的默认样式 - 使用最高优先级选择器
  .carousel-item-content.workout-data-content .workout-exercises-section .exercise-list.simplified-exercise-list .exercise-items .exercise-item .exercise-image {
    width: 60px !important;
    height: 60px !important;
    min-width: 60px !important;
    min-height: 60px !important;
    border-radius: 50% !important;
    border: none !important;
    background: white !important;

    .exercise-icon svg {
      width: 30px !important;
      height: 30px !important;
    }
  }

  // 额外的强制样式 - 针对所有可能的exercise-image
  .exercise-image {
    width: 60px !important;
    height: 60px !important;
    min-width: 60px !important;
    min-height: 60px !important;
    border-radius: 50% !important;
    border: none !important;
    background: white !important;
  }

  // 空状态
  &.empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    
    .empty-state {
      color: var(--text-tertiary);
      font-size: var(--text-sm);
    }
  }
}

// 轮播容器
.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

// 轮播轨道
.carousel-track {
  display: flex;
  width: 100%;
  height: 100%;
  will-change: transform;
  
  // iOS硬件加速优化
  transform: translateZ(0);
  backface-visibility: hidden;
}

// 轮播项
.carousel-item {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &.active {
    z-index: 1;
  }
}

// 轮播项内容
.carousel-item-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

// 肌肉示意图内容
.muscle-illustration-content {
  .muscle-illustration-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-height: 300px;
    
    .carousel-muscle-illustration {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
    }
  }
  
  .muscle-stats {
    display: flex;
    gap: var(--space-6);
    margin-top: var(--space-4);
    padding: var(--space-3) var(--space-4);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    
    .muscle-count,
    .intensity-level {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--space-1);
      
      .count,
      .level {
        font-size: var(--text-lg);
        font-weight: var(--font-semibold);
        color: var(--accent-500);
      }
      
      .label {
        font-size: var(--text-xs);
        color: var(--text-secondary);
      }
    }
  }
}

// 用户图像内容
.user-image-content {
  .user-image {
    width: 100%;
    height: auto;
    max-height: 300px;
    object-fit: cover;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
  }

  .image-caption {
    margin-top: var(--space-3);
    padding: var(--space-2) var(--space-3);
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-size: var(--text-sm);
    color: var(--text-secondary);
    text-align: center;
  }
}

// 训练数据内容（整合动作列表和肌肉示意图）- 左右分栏布局
.workout-data-content {
  display: flex;
  flex-direction: row; // 改为左右布局
  gap: 0; // 移除gap，使用精确的百分比控制
  height: 100%;
  padding: var(--space-4);
  align-items: flex-start; // 确保左右区域顶部对齐
  position: relative; // 为底部查看更多标签提供定位基准

  // 左侧：简化的动作列表 (25%)
  .workout-exercises-section {
    flex: 0 0 25%; // 固定25%宽度
    padding-right: 5%; // 右侧间距的一半
    min-width: 120px; // 确保在小屏幕上有最小宽度
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: stretch;

    .simplified-exercise-list {
      background: transparent;
      flex: 1;
      display: flex;
      flex-direction: column;

      // 覆盖ExerciseList的默认样式
      .exercise-items {
        gap: 0; // 移除默认间距，使用margin-bottom控制
        flex: 1;
      }

      // 简化动作项显示
      .exercise-item {
        padding: 2px 0;
        margin-bottom: 3px;
        display: flex;
        align-items: center;
        gap: var(--space-2);
        background: transparent; // 移除动作项的背景色
        min-height: auto; // 移除最小高度限制
        border: none; // 去除外框

        // 取消hover交互效果
        &:hover {
          background: transparent;
          transform: none;
          box-shadow: none;
        }

        // 最大化动作图像尺寸，确保标准圆形，去除外框
        .exercise-image {
          width: 60px !important;
          height: 60px !important;
          min-width: 60px !important; // 防止压缩变形
          min-height: 60px !important; // 防止压缩变形
          border-radius: 50% !important; // 确保标准圆形容器
          background: white !important; // 白色背景
          overflow: hidden;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          border: none !important; // 去除外框
          position: relative; // 确保内容正确定位

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%; // 确保图像也是圆形
          }

          .exercise-icon {
            width: 30px !important;
            height: 30px !important;
            color: var(--text-tertiary);

            svg {
              width: 30px !important;
              height: 30px !important;
            }
          }
        }

        .exercise-info {
          flex: 1;
          min-width: 0;
          display: flex;
          flex-direction: column;
          justify-content: center;

          .exercise-name {
            font-size: var(--text-xs);
            line-height: 1.1;
            font-weight: var(--font-medium);
            color: var(--text-primary);
            margin-bottom: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap; // 防止文本换行
          }

          .exercise-details {
            font-size: 10px;
            color: var(--text-secondary);
            line-height: 1.2;

            .sets-info {
              display: inline;
            }

            // 隐藏详细信息，只显示组数
            .reps-info,
            .weight-info {
              display: none;
            }
          }

          // 隐藏肌肉群信息
          .muscle-groups {
            display: none;
          }
        }

        // 隐藏动作序号
        .exercise-index {
          display: none;
        }
      }

      // 隐藏exercise-summary
      .exercise-summary {
        display: none;
      }

      // 隐藏查看更多按钮
      .view-more-section {
        display: none;
      }

      // 查看详情按钮样式
      .view-details-section {
        display: flex;
        justify-content: center;
        margin-top: var(--space-2);

        .view-details-btn {
          padding: var(--space-1) var(--space-3);
          background: var(--accent-500);
          color: white;
          border: none;
          border-radius: var(--radius-sm);
          font-size: var(--text-xs);
          font-weight: var(--font-medium);
          cursor: pointer;
          transition: all var(--transition-normal) var(--ease-in-out);

          &:hover {
            background: var(--accent-600);
            transform: translateY(-1px);
          }

          &:active {
            transform: translateY(0);
          }
        }
      }
    }
  }

  // 右侧：肌肉示意图 (65%) - 简化版本
  .workout-muscle-section {
    flex: 0 0 65%; // 固定65%宽度
    padding-left: 5%; // 左侧间距的一半
    min-width: 200px; // 确保在小屏幕上有最小宽度
    display: flex;
    align-items: flex-start;
    justify-content: center;

    .carousel-muscle-illustration {
      // 应用 h-auto w-full 样式类
      &.h-auto {
        height: auto;
      }

      &.w-full {
        width: 100%;
      }

      max-height: 100%;
      object-fit: contain;
    }
  }

  // 底部查看更多标签
  .view-more-label {
    position: absolute;
    bottom: var(--space-1);
    left: 50%;
    transform: translateX(-50%);
    font-size: var(--text-xs);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-normal) var(--ease-in-out);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--radius-sm);

    &:hover {
      color: var(--accent-500);
      background: var(--bg-secondary);
    }

    &:active {
      transform: translateX(-50%) scale(0.95);
    }
  }

  // 强制左右布局 - 移除响应式切换，在所有屏幕尺寸下都保持左右分栏
  // 保持左侧动作列表25%，右侧肌肉图65%的比例
}

// 导航按钮
.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: var(--ios-touch-target);
  height: var(--ios-touch-target);
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-normal) var(--ease-in-out);
  box-shadow: var(--shadow-md);
  z-index: 2;
  
  // iOS触摸优化
  -webkit-tap-highlight-color: transparent;
  
  &:hover {
    background: rgba(255, 255, 255, 1);
    transform: translateY(-50%) scale(1.05);
  }
  
  &:active {
    transform: translateY(-50%) scale(0.95);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: translateY(-50%);
  }
  
  &.prev {
    left: var(--space-3);
  }
  
  &.next {
    right: var(--space-3);
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
}

// 指示器
.carousel-indicators {
  position: absolute;
  bottom: var(--space-3);
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: var(--space-2);
  z-index: 2;
  
  .indicator {
    width: 8px;
    height: 8px;
    border: none;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all var(--transition-fast) var(--ease-in-out);
    
    // iOS触摸优化
    -webkit-tap-highlight-color: transparent;
    min-width: var(--ios-touch-target);
    min-height: var(--ios-touch-target);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: currentColor;
      transition: all var(--transition-fast) var(--ease-in-out);
    }
    
    &.active {
      background: rgba(255, 255, 255, 0.9);
      
      &::before {
        background: var(--accent-500);
        transform: scale(1.2);
      }
    }
    
    &:hover:not(.active) {
      background: rgba(255, 255, 255, 0.7);
    }
  }
}

// 暗色主题适配
.theme-dark {
  .carousel-nav {
    background: rgba(30, 41, 59, 0.9);
    color: var(--text-primary);

    &:hover {
      background: rgba(30, 41, 59, 1);
    }
  }

  .carousel-indicators .indicator {
    background: rgba(30, 41, 59, 0.5);

    &.active {
      background: rgba(30, 41, 59, 0.9);
    }

    &:hover:not(.active) {
      background: rgba(30, 41, 59, 0.7);
    }
  }

  .workout-data-content .view-more-label {
    color: var(--text-secondary);

    &:hover {
      color: var(--accent-400);
      background: var(--bg-tertiary);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  // 移动端强制覆盖样式
  .workout-carousel .workout-data-content .simplified-exercise-list .exercise-items .exercise-item .exercise-image {
    width: 60px !important;
    height: 60px !important;
    min-width: 60px !important;
    min-height: 60px !important;
    border-radius: 50% !important;
    border: none !important;
    background: white !important;

    .exercise-icon svg {
      width: 30px !important;
      height: 30px !important;
    }
  }

  // 移动端额外强制样式
  .exercise-image {
    width: 60px !important;
    height: 60px !important;
    min-width: 60px !important;
    min-height: 60px !important;
    border-radius: 50% !important;
    border: none !important;
    background: white !important;
  }

  .carousel-nav {
    width: 36px;
    height: 36px;

    svg {
      width: 16px;
      height: 16px;
    }

    &.prev {
      left: var(--space-2);
    }

    &.next {
      right: var(--space-2);
    }
  }
  
  .carousel-indicators {
    bottom: var(--space-2);
    
    .indicator {
      width: 6px;
      height: 6px;
      
      &::before {
        width: 6px;
        height: 6px;
      }
    }
  }
  
  .muscle-illustration-content {
    .muscle-illustration-wrapper {
      max-height: 250px;
    }
    
    .muscle-stats {
      gap: var(--space-4);
      margin-top: var(--space-3);
      padding: var(--space-2) var(--space-3);
    }
  }
  
  .user-image-content .user-image {
    max-height: 250px;
  }
}

// 减少动画支持
@media (prefers-reduced-motion: reduce) {
  .carousel-track {
    transition: none !important;
  }
  
  .carousel-nav,
  .carousel-indicators .indicator {
    transition: none;
  }
  
  .carousel-nav:hover,
  .carousel-nav:active {
    transform: translateY(-50%);
  }
}
