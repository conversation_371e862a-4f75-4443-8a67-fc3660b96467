import React, { useState, useRef, useEffect, useCallback } from 'react';
import { WorkoutCarouselProps, CarouselItem } from '../../../types/feed.types';
import { StaticMuscleIllustration } from '../MuscleVisualization/StaticMuscleIllustration';
import { ExerciseList } from '../ExerciseList/ExerciseList';
import './WorkoutCarousel.scss';

export const WorkoutCarousel: React.FC<WorkoutCarouselProps> = ({
  items,
  autoPlay = false,
  showIndicators = true,
  className = '',
  onItemChange
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const carouselRef = useRef<HTMLDivElement>(null);
  const touchStartX = useRef<number>(0);
  const touchEndX = useRef<number>(0);

  // 自动播放逻辑
  useEffect(() => {
    if (!autoPlay || items.length <= 1) return;

    const interval = setInterval(() => {
      handleNext();
    }, 5000); // 5秒切换

    return () => clearInterval(interval);
  }, [autoPlay, items.length, currentIndex]);

  // 切换到下一项
  const handleNext = useCallback(() => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    const nextIndex = (currentIndex + 1) % items.length;
    setCurrentIndex(nextIndex);
    onItemChange?.(nextIndex);
    
    setTimeout(() => setIsTransitioning(false), 300);
  }, [currentIndex, items.length, isTransitioning, onItemChange]);

  // 切换到上一项
  const handlePrev = useCallback(() => {
    if (isTransitioning) return;
    
    setIsTransitioning(true);
    const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    onItemChange?.(prevIndex);
    
    setTimeout(() => setIsTransitioning(false), 300);
  }, [currentIndex, items.length, isTransitioning, onItemChange]);

  // 切换到指定项
  const handleGoTo = useCallback((index: number) => {
    if (isTransitioning || index === currentIndex) return;
    
    setIsTransitioning(true);
    setCurrentIndex(index);
    onItemChange?.(index);
    
    setTimeout(() => setIsTransitioning(false), 300);
  }, [currentIndex, isTransitioning, onItemChange]);

  // 触摸事件处理
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.touches[0].clientX;
  };

  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;
    
    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      handleNext();
    } else if (isRightSwipe) {
      handlePrev();
    }

    touchStartX.current = 0;
    touchEndX.current = 0;
  };

  // 渲染轮播项内容
  const renderCarouselItem = (item: CarouselItem) => {
    switch (item.type) {
      case 'muscle_illustration':
        const muscleData = item.content.muscle_data;
        if (!muscleData) return null;
        
        return (
          <div className="carousel-item-content muscle-illustration-content">
            <div className="muscle-illustration-wrapper">
              <StaticMuscleIllustration
                selectedMuscles={muscleData.selectedMuscles}
                muscleColorConfig={muscleData.muscleColorConfig}
                theme="light"
                className="carousel-muscle-illustration"
              />
            </div>
            <div className="muscle-stats">
              <div className="muscle-count">
                <span className="count">{muscleData.selectedMuscles.length}</span>
                <span className="label">个肌群</span>
              </div>
              <div className="intensity-level">
                <span className="level">
                  {muscleData.intensities.length > 5 ? '高强度' : 
                   muscleData.intensities.length > 2 ? '中强度' : '轻度'}
                </span>
                <span className="label">训练</span>
              </div>
            </div>
          </div>
        );

      case 'user_image':
        const imageData = item.content.image_data;
        if (!imageData) return null;

        return (
          <div className="carousel-item-content user-image-content">
            <img
              src={imageData.url}
              alt={imageData.alt}
              className="user-image"
              loading="lazy"
            />
            {imageData.caption && (
              <div className="image-caption">
                {imageData.caption}
              </div>
            )}
          </div>
        );

      case 'workout_data':
        const workoutData = item.content.workout_data;
        if (!workoutData) return null;

        return (
          <div className="carousel-item-content workout-data-content">
            {/* 左侧：简化的动作列表 (25%) */}
            <div className="workout-exercises-section">
              <ExerciseList
                exercises={workoutData.exercises}
                maxVisible={4}
                showImages={true}
                className="simplified-exercise-list"
              />
              {/* 当动作数量超过4个时，显示查看详情按钮 */}
              {workoutData.exercises.length > 4 && (
                <div className="view-details-section">
                  <button className="view-details-btn">
                    查看详情
                  </button>
                </div>
              )}
            </div>

            {/* 右侧：肌肉示意图 (65%) */}
            <div className="workout-muscle-section">
              <StaticMuscleIllustration
                selectedMuscles={workoutData.selectedMuscles}
                muscleColorConfig={workoutData.muscleColorConfig}
                theme="light"
                className="carousel-muscle-illustration h-auto w-full"
              />
            </div>

            {/* 底部查看更多标签 */}
            <div className="view-more-label">
              查看更多
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  if (!items || items.length === 0) {
    return (
      <div className={`workout-carousel empty ${className}`}>
        <div className="empty-state">
          <span>暂无内容</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`workout-carousel ${className}`}>
      <div 
        className="carousel-container"
        ref={carouselRef}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div 
          className="carousel-track"
          style={{
            transform: `translateX(-${currentIndex * 100}%)`,
            transition: isTransitioning ? 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)' : 'none'
          }}
        >
          {items.map((item, index) => (
            <div 
              key={item.id}
              className={`carousel-item ${index === currentIndex ? 'active' : ''}`}
            >
              {renderCarouselItem(item)}
            </div>
          ))}
        </div>

        {/* 导航按钮 */}
        {items.length > 1 && (
          <>
            <button 
              className="carousel-nav prev"
              onClick={handlePrev}
              disabled={isTransitioning}
              aria-label="上一项"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M15 18L9 12L15 6" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </button>
            
            <button 
              className="carousel-nav next"
              onClick={handleNext}
              disabled={isTransitioning}
              aria-label="下一项"
            >
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                <path 
                  d="M9 18L15 12L9 6" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          </>
        )}
      </div>

      {/* 指示器 */}
      {showIndicators && items.length > 1 && (
        <div className="carousel-indicators">
          {items.map((_, index) => (
            <button
              key={index}
              className={`indicator ${index === currentIndex ? 'active' : ''}`}
              onClick={() => handleGoTo(index)}
              aria-label={`切换到第${index + 1}项`}
            />
          ))}
        </div>
      )}
    </div>
  );
};
