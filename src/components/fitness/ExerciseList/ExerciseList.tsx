import React, { useState, useMemo } from 'react';
import { ExerciseListProps, WorkoutExercise } from '../../../types/feed.types';
import { MUSCLE_DISPLAY_NAMES } from '../../../types/muscle.types';
import './ExerciseList.scss';

export const ExerciseList: React.FC<ExerciseListProps> = ({
  exercises,
  maxVisible = 3,
  showImages = true,
  onViewMore,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // 计算显示的动作列表
  const displayedExercises = useMemo(() => {
    if (isExpanded || exercises.length <= maxVisible) {
      return exercises;
    }
    return exercises.slice(0, maxVisible);
  }, [exercises, maxVisible, isExpanded]);

  // 是否需要显示"查看更多"按钮
  const needsViewMore = exercises.length > maxVisible;

  // 处理查看更多点击
  const handleViewMore = () => {
    if (onViewMore) {
      onViewMore();
    } else {
      setIsExpanded(!isExpanded);
    }
  };

  // 获取动作的主要肌肉群显示名称
  const getPrimaryMuscleNames = (exercise: WorkoutExercise): string => {
    return exercise.primary_muscles
      .map(muscle => MUSCLE_DISPLAY_NAMES[muscle] || muscle)
      .join('、');
  };

  // 渲染动作项
  const renderExerciseItem = (exercise: WorkoutExercise, index: number) => (
    <div key={exercise.id} className="exercise-item">
      {/* 动作缩略图 */}
      {showImages && (
        <div className="exercise-image">
          {exercise.image_url ? (
            <img 
              src={exercise.image_url} 
              alt={exercise.name}
              loading="lazy"
              onError={(e) => {
                // 图片加载失败时显示默认图标
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
                target.nextElementSibling?.classList.remove('hidden');
              }}
            />
          ) : null}
          <div className={`exercise-icon ${exercise.image_url ? 'hidden' : ''}`}>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
              <path 
                d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z" 
                fill="currentColor"
              />
            </svg>
          </div>
        </div>
      )}

      {/* 动作信息 */}
      <div className="exercise-info">
        <div className="exercise-name">{exercise.name}</div>
        <div className="exercise-details">
          <span className="sets-info">{exercise.sets} 组</span>
          {exercise.reps > 0 && (
            <span className="reps-info">× {exercise.reps}</span>
          )}
          {exercise.weight > 0 && (
            <span className="weight-info">{exercise.weight}kg</span>
          )}
        </div>
        <div className="muscle-groups">
          {getPrimaryMuscleNames(exercise)}
        </div>
      </div>

      {/* 动作序号 */}
      <div className="exercise-index">
        {index + 1}
      </div>
    </div>
  );

  if (!exercises || exercises.length === 0) {
    return (
      <div className={`exercise-list empty ${className}`}>
        <div className="empty-state">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none">
            <path 
              d="M20.57 14.86L22 13.43 20.57 12 17 15.57 8.43 7 12 3.43 10.57 2 9.14 3.43 7.71 2 5.57 4.14 4.14 2.71 2.71 4.14l1.43 1.43L2 7.71l1.43 1.43L2 10.57 3.43 12 7 8.43 15.57 17 12 20.57 13.43 22l1.43-1.43L16.29 22l2.14-2.14 1.43 1.43 1.43-1.43-1.43-1.43L22 16.29l-1.43-1.43z" 
              fill="currentColor"
            />
          </svg>
          <span>暂无训练动作</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`exercise-list ${className}`}>
      {/* 动作列表 */}
      <div className="exercise-items">
        {displayedExercises.map((exercise, index) => 
          renderExerciseItem(exercise, index)
        )}
      </div>

      {/* 查看更多按钮 */}
      {needsViewMore && (
        <div className="view-more-section">
          <button 
            className="view-more-btn"
            onClick={handleViewMore}
          >
            <span className="btn-text">
              {isExpanded ? '收起' : `查看更多 (${exercises.length - maxVisible})`}
            </span>
            <svg 
              className={`btn-icon ${isExpanded ? 'rotated' : ''}`}
              width="16" 
              height="16" 
              viewBox="0 0 24 24" 
              fill="none"
            >
              <path 
                d="M6 9L12 15L18 9" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      )}

      {/* 训练统计摘要 */}
      <div className="exercise-summary">
        <div className="summary-item">
          <span className="summary-value">{exercises.length}</span>
          <span className="summary-label">个动作</span>
        </div>
        <div className="summary-item">
          <span className="summary-value">
            {exercises.reduce((total, ex) => total + ex.sets, 0)}
          </span>
          <span className="summary-label">总组数</span>
        </div>
        <div className="summary-item">
          <span className="summary-value">
            {exercises.reduce((total, ex) => total + (ex.sets * ex.weight * ex.reps), 0).toFixed(0)}
          </span>
          <span className="summary-label">总重量(kg)</span>
        </div>
      </div>
    </div>
  );
};
