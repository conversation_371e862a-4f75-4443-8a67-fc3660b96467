{"name": "fitmaster", "private": true, "description": "FitMaster健身应用 - 基于React和TypeScript的现代健身追踪应用", "version": "1.0.0", "type": "module", "main": "src/index.tsx", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "type-check": "tsc --noEmit", "capacitor:init": "cap init FitMaster com.fitmaster.app", "capacitor:add:ios": "cap add ios", "capacitor:sync": "cap sync", "capacitor:sync:ios": "cap sync ios", "capacitor:copy": "cap copy", "capacitor:open:ios": "cap open ios", "build:ios": "npm run build && cap sync ios && cap open ios", "dev:ios": "cap run ios --livereload --external", "deploy:ios": "npm run build && cap sync ios", "health:check": "node -e \"console.log('FitMaster Health Check - All systems ready!')\"", "api:test": "node -e \"fetch('http://**************:8000/api/v1/health').then(r=>r.json()).then(console.log).catch(console.error)\""}, "keywords": ["fitness", "react", "typescript", "fitmaster", "workout", "health", "tracking"], "author": "Fitness Team", "license": "MIT", "dependencies": {"@capacitor/app": "^5.0.7", "@capacitor/camera": "^5.0.9", "@capacitor/core": "^5.6.0", "@capacitor/device": "^5.0.7", "@capacitor/ios": "^5.6.0", "@capacitor/keyboard": "^5.0.8", "@capacitor/local-notifications": "^5.0.7", "@capacitor/network": "^5.0.7", "@capacitor/preferences": "^5.0.7", "@capacitor/push-notifications": "^5.1.0", "@capacitor/splash-screen": "^5.0.7", "@capacitor/status-bar": "^5.0.7", "@hackernoon/pixel-icon-library": "^1.0.6", "@heroui/button": "^2.2.23", "@heroui/card": "^2.2.22", "@heroui/chip": "^2.2.19", "@heroui/image": "^2.2.15", "@heroui/input": "^2.4.24", "@heroui/modal": "^2.2.20", "@heroui/react": "^2.8.1", "@heroui/select": "^2.4.24", "@heroui/system": "^2.4.19", "@heroui/theme": "^2.4.19", "framer-motion": "^11.18.2", "lucide-react": "^0.525.0", "pixel-retroui": "^2.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.0"}, "devDependencies": {"@capacitor/cli": "^5.6.0", "@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-react": "^0.6.2", "@tailwindcss/postcss": "^4.1.11", "@types/jest": "^30.0.0", "@types/node": "^20.10.0", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.5.6", "sass": "^1.69.5", "tailwindcss": "^4.1.11", "typescript": "^5.2.2", "vite": "^5.0.8"}}